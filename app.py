from flask import Flask, render_template, jsonify, request, make_response
from flask_cors import CORS
import os
import jwt
import datetime
from functools import wraps

app = Flask(__name__)
CORS(app)  # 启用CORS，允许跨域请求

# 配置密钥，实际生产环境应该使用环境变量
app.config['SECRET_KEY'] = 'your-secret-key-here'  # 请在生产环境中更改为安全的密钥

# 模拟用户数据库
USERS = {
    'admin': 'admin123',
    'user1': 'password1',
    'test': 'test123'
}

def token_required(f):
    @wraps(f)
    def decorated(*args, **kwargs):
        if request.method == 'OPTIONS':
            # 处理预检请求
            response = make_response()
            response.headers.add('Access-Control-Allow-Origin', '*')
            response.headers.add('Access-Control-Allow-Headers', 'Content-Type,Authorization')
            response.headers.add('Access-Control-Allow-Methods', 'GET,POST,OPTIONS')
            return response
            
        auth_header = request.headers.get('Authorization')
        
        if not auth_header:
            return jsonify({
                'code': 401,
                'message': '未提供认证令牌',
                'data': None
            }), 401
            
        try:
            # 从 'Bearer <token>' 中提取token
            parts = auth_header.split()
            if parts[0].lower() != 'bearer' or len(parts) != 2:
                raise ValueError('Invalid token header')
                
            token = parts[1]
            data = jwt.decode(token, app.config['SECRET_KEY'], algorithms=['HS256'])
            current_user = data['username']
            
        except jwt.ExpiredSignatureError:
            return jsonify({
                'code': 401,
                'message': '令牌已过期',
                'data': None
            }), 401
            
        except (jwt.InvalidTokenError, ValueError) as e:
            print(f"Token error: {str(e)}")
            return jsonify({
                'code': 401,
                'message': '无效的认证令牌',
                'data': None
            }), 401
            
        except Exception as e:
            print(f"Unexpected error: {str(e)}")
            return jsonify({
                'code': 500,
                'message': '服务器内部错误',
                'data': None
            }), 500
            
        return f(current_user, *args, **kwargs)
    
    return decorated

# 确保静态文件目录存在
if not os.path.exists('static'):
    os.makedirs('static')
if not os.path.exists('static/images'):
    os.makedirs('static/images')

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/api/login', methods=['POST'])
def login():
    # 确保请求包含 JSON 数据
    if not request.is_json:
        return jsonify({'message': 'Missing JSON in request'}), 415
        
    auth = request.get_json()
    
    if not auth or not auth.get('username') or not auth.get('password'):
        return jsonify({'message': '用户名和密码不能为空'}), 400
    
    username = auth.get('username')
    password = auth.get('password')
    
    # 验证用户名和密码
    if username not in USERS or USERS[username] != password:
        return jsonify({'message': '用户名或密码错误'}), 401
    
    try:
        # 生成JWT token
        token = jwt.encode(
            {
                'username': username,
                'exp': datetime.datetime.utcnow() + datetime.timedelta(hours=24)  # 24小时过期
            },
            app.config['SECRET_KEY'],
            algorithm='HS256'
        )
        
        # 确保 token 是字符串（PyJWT 2.0+ 返回的是 bytes）
        if isinstance(token, bytes):
            token = token.decode('utf-8')
            
        return jsonify({
            'code': 200,
            'message': '登录成功',
            'data': {
                'token': token,
                'username': username
            }
        }), 200
    except Exception as e:
        print(f"Token generation error: {str(e)}")
        return jsonify({'message': '服务器内部错误'}), 500

@app.route('/api/verify_token', methods=['POST'])
def verify_token():
    token = request.headers.get('Authorization')
    
    if not token:
        return jsonify({'valid': False, 'message': 'Token is missing!'}), 401
        
    try:
        # 从 'Bearer <token>' 中提取token
        token = token.split(' ')[1]
        data = jwt.decode(token, app.config['SECRET_KEY'], algorithms=['HS256'])
        return jsonify({
            'valid': True,
            'username': data['username']
        }), 200
    except Exception as e:
        return jsonify({'valid': False, 'message': 'Token is invalid!'}), 401

@app.route('/api/foods', methods=['GET', 'OPTIONS'])  
@token_required
def get_foods(current_user):
    if request.method == 'OPTIONS':
        # 处理预检请求
        response = make_response()
        response.headers.add('Access-Control-Allow-Origin', '*')
        response.headers.add('Access-Control-Allow-Headers', 'Content-Type,Authorization')
        response.headers.add('Access-Control-Allow-Methods', 'GET,POST,OPTIONS')
        return response
        
    try:
        foods = [
            {
                "foodName": "沙拉碗",
                "foodPrice": 25,
                "image": "https://img.freepik.com/free-photo/greek-salad-with-fresh-vegetables-feta-cheese-kalamata-olives_2829-10854.jpg?w=600&h=400&fit=crop",
                "foodDescription": "新鲜制作的沙拉碗，选用当季蔬菜与优质生菜搭配，富含膳食纤维和多种维生素，低脂酱料让口感更清爽，是健康饮食的首选。",
                "stock": 1
            },
            {
                "foodName": "辣粮碗",
                "foodPrice": 22,
                "image": "https://img.freepik.com/free-photo/top-view-pepperoni-pizza-with-mushroom-sausages-bell-pepper-olive-corn-black-wooden_141793-2158.jpg?w=600&h=400&fit=crop",
                "foodDescription": "辣粮碗由五种精选粗粮精心熬制而成，富含丰富的碳水化合物和微量元素，有助于增强饱腹感，适合健身人士和追求健康的你。",
                "stock": 80
            },
            {
                "foodName": "酸奶碗",
                "foodPrice": 18,
                "image": "https://img.freepik.com/free-photo/side-view-fried-chicken-wooden-board-with-french-fries-salad-sauce_141793-4887.jpg?w=600&h=400&fit=crop",
                "foodDescription": "酸奶碗采用天然发酵酸奶，搭配少量蜂蜜和坚果，口感醇厚，富含益生菌，帮助调节肠道菌群，提升消化能力。",
                "stock": 120
            },
            {
                "foodName": "时令果汁",
                "foodPrice": 15,
                "image": "https://img.freepik.com/free-photo/front-view-burger-stand_141793-15542.jpg?w=600&h=400&fit=crop",
                "foodDescription": "时令果汁选用当季水果鲜榨而成，无添加糖分，保留水果原汁原味，营养丰富，是补充维生素的理想选择。",
                "stock": 150
            }
        ]
        return jsonify({
            'code': 200,
            'message': '获取食物列表成功',
            'data': foods
        })
    except Exception as e:
        print(f"Error in get_foods: {str(e)}")
        return jsonify({
            'code': 500,
            'message': '获取食物列表失败',
            'data': None
        }), 500

def api_response(code, message, data=None):
    response = {
        "code": code,
        "message": message
    }
    if data is not None:
        response["data"] = data
    return jsonify(response)

if __name__ == '__main__':
    app.run(debug=True)
