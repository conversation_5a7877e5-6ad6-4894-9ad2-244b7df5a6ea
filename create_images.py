from PIL import Image, ImageDraw, ImageFont
import os

# 确保目录存在
if not os.path.exists('static/images'):
    os.makedirs('static/images')

# 创建食品图片
def create_food_image(filename, text, color):
    # 创建一个300x200的图片
    img = Image.new('RGB', (300, 200), color=color)
    draw = ImageDraw.Draw(img)
    
    # 尝试加载字体，如果失败则使用默认字体
    try:
        font = ImageFont.truetype("arial.ttf", 20)
    except IOError:
        font = ImageFont.load_default()
    
    # 在图片中央添加文本
    text_width, text_height = draw.textsize(text, font=font)
    position = ((300 - text_width) // 2, (200 - text_height) // 2)
    draw.text(position, text, fill="white", font=font)
    
    # 保存图片
    img.save(f'static/images/{filename}')
    print(f'已创建图片: {filename}')

# 创建四个食品图片
create_food_image('food1.png', '沙拉碗', (76, 175, 80))  # 绿色
create_food_image('food2.png', '辣粮碗', (217, 83, 79))  # 红色
create_food_image('food3.png', '酸奶碗', (91, 192, 222)) # 蓝色
create_food_image('food4.png', '时令果汁', (240, 173, 78)) # 黄色

print('所有图片创建完成！')
